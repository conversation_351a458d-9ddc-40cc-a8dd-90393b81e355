# Configuration file

##########################################################################################################
# Forge
#--------------------------------------------------------------------------------------------------------#
# Sample mod specific control section.
# Copy this section and rename the with the modid for the mod you wish to override.
# A value of zero in either entry effectively disables any chunkloading capabilities
# for that mod
##########################################################################################################

Forge {
    # Maximum chunks per ticket for the mod.
    I:maximumChunksPerTicket=25

    # Maximum ticket count for the mod. Zero disables chunkloading capabilities.
    I:maximumTicketCount=200
}


##########################################################################################################
# defaults
#--------------------------------------------------------------------------------------------------------#
# Default configuration for forge chunk loading control
##########################################################################################################

defaults {
    # Are mod overrides enabled?
    B:enabled=true

    # The default maximum number of chunks a mod can force, per ticket, 
    # for a mod without an override. This is the maximum number of chunks a single ticket can force.
    I:maximumChunksPerTicket=25

    # The default maximum ticket count for a mod which does not have an override
    # in this file. This is the number of chunk loading requests a mod is allowed to make.
    I:maximumTicketCount=200

    # The number of tickets a player can be assigned instead of a mod. This is shared across all mods and it is up to the mods to use it.
    I:playerTicketCount=500

    # Unloaded chunks can first be kept in a dormant cache for quicker
    # loading times. Specify the size (in chunks) of that cache here
    I:dormantChunkCacheSize=0
}


