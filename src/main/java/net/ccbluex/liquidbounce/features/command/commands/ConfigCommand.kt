/*
 * LiquidBounce Hacked Client
 * A free open source mixin-based injection hacked client for Minecraft using Minecraft Forge.
 * https://github.com/CCBlueX/LiquidBounce/
 */
package net.ccbluex.liquidbounce.features.command.commands

import com.google.gson.JsonObject
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import net.ccbluex.liquidbounce.LiquidBounce.commandManager
import net.ccbluex.liquidbounce.LiquidBounce.moduleManager
import net.ccbluex.liquidbounce.utils.kotlin.SharedScopes
import net.ccbluex.liquidbounce.features.command.Command
import net.ccbluex.liquidbounce.file.FileManager.PRETTY_GSON
import net.ccbluex.liquidbounce.file.FileManager.settingsDir
import net.ccbluex.liquidbounce.ui.client.hud.HUD.addNotification
import net.ccbluex.liquidbounce.ui.client.hud.element.elements.Notification
import net.ccbluex.liquidbounce.utils.io.readJson
import java.awt.Desktop
import java.io.File
import java.io.IOException

object ConfigCommand : Command("config", "cfg") {

    /**
     * Execute commands with provided [args]
     */
    override fun execute(args: Array<String>) {
        if (args.size <= 1) {
            chatSyntax("config <save/load/list/delete/folder> [name]")
            return
        }

        SharedScopes.IO.launch {
            when (args[1].lowercase()) {
                "save" -> saveConfig(args)
                "load" -> loadConfig(args)
                "list" -> listConfigs()
                "delete" -> deleteConfig(args)
                "folder" -> openConfigFolder()
                else -> chatSyntax("config <save/load/list/delete/folder> [name]")
            }
        }
    }

    private suspend fun saveConfig(args: Array<String>) {
        withContext(Dispatchers.IO) {
            if (args.size <= 2) {
                chatSyntax("config save <name>")
                return@withContext
            }

            val configName = args[2]
            val configFile = File(settingsDir, "$configName.json")

            try {
                chat("§9Saving configuration '$configName'...")

                // Create the main config object
                val configObject = JsonObject()

                // Save basic client info
                configObject.addProperty("ConfigName", configName)
                configObject.addProperty("ClientVersion", net.ccbluex.liquidbounce.LiquidBounce.clientVersionText)
                configObject.addProperty("SaveTime", System.currentTimeMillis())
                configObject.addProperty("CommandPrefix", commandManager.prefix)

                // Save all modules (states, keybinds, and values)
                val modulesObject = JsonObject()
                for (module in moduleManager) {
                    val moduleObject = JsonObject()
                    
                    // Save module state and keybind
                    moduleObject.addProperty("State", module.state)
                    moduleObject.addProperty("KeyBind", module.keyBind)
                    
                    // Save module values if any
                    if (module.values.isNotEmpty()) {
                        val valuesObject = JsonObject()
                        for (value in module.values) {
                            valuesObject.add(value.name, value.toJson())
                        }
                        moduleObject.add("Values", valuesObject)
                    }
                    
                    modulesObject.add(module.name, moduleObject)
                }
                configObject.add("Modules", modulesObject)

                // Write to file
                if (configFile.exists()) {
                    configFile.delete()
                }
                configFile.createNewFile()
                configFile.writeText(PRETTY_GSON.toJson(configObject))

                chat("§aConfiguration '$configName' saved successfully!")
                addNotification(Notification.informative("Config Command", "Configuration '$configName' saved"))
                playEdit()

            } catch (e: Exception) {
                chat("§cFailed to save configuration: ${e.message}")
                e.printStackTrace()
            }
        }
    }

    private suspend fun loadConfig(args: Array<String>) {
        withContext(Dispatchers.IO) {
            if (args.size <= 2) {
                chatSyntax("config load <name>")
                return@withContext
            }

            val configName = args[2]
            val configFile = File(settingsDir, "$configName.json")

            if (!configFile.exists()) {
                chat("§cConfiguration '$configName' does not exist!")
                return@withContext
            }

            try {
                chat("§9Loading configuration '$configName'...")

                val configObject = configFile.readJson() as? JsonObject
                if (configObject == null) {
                    chat("§cInvalid configuration file format!")
                    return@withContext
                }

                // Load command prefix if present
                configObject.get("CommandPrefix")?.asString?.let { prefix ->
                    commandManager.prefix = prefix
                }

                // Load modules
                val modulesObject = configObject.getAsJsonObject("Modules")
                if (modulesObject != null) {
                    for ((moduleName, moduleData) in modulesObject.entrySet()) {
                        val module = moduleManager[moduleName]
                        if (module == null) {
                            chat("§eWarning: Module '$moduleName' not found, skipping...")
                            continue
                        }

                        val moduleObject = moduleData as JsonObject

                        // Load module state
                        moduleObject.get("State")?.asBoolean?.let { state ->
                            module.state = state
                        }

                        // Load module keybind
                        moduleObject.get("KeyBind")?.asInt?.let { keyBind ->
                            module.keyBind = keyBind
                        }

                        // Load module values
                        val valuesObject = moduleObject.getAsJsonObject("Values")
                        if (valuesObject != null) {
                            for (value in module.values) {
                                val element = valuesObject.get(value.name)
                                if (element != null) {
                                    try {
                                        value.fromJson(element)
                                    } catch (e: Exception) {
                                        chat("§eWarning: Failed to load value '${value.name}' for module '$moduleName': ${e.message}")
                                    }
                                }
                            }
                        }
                    }
                }

                // Save the loaded configuration to current configs
                net.ccbluex.liquidbounce.file.FileManager.saveConfig(net.ccbluex.liquidbounce.file.FileManager.valuesConfig)
                net.ccbluex.liquidbounce.file.FileManager.saveConfig(net.ccbluex.liquidbounce.file.FileManager.modulesConfig)

                val saveTime = configObject.get("SaveTime")?.asLong
                val timeInfo = if (saveTime != null) {
                    val date = java.util.Date(saveTime)
                    " (saved: ${java.text.SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(date)})"
                } else ""

                chat("§aConfiguration '$configName' loaded successfully!$timeInfo")
                addNotification(Notification.informative("Config Command", "Configuration '$configName' loaded"))
                playEdit()

            } catch (e: Exception) {
                chat("§cFailed to load configuration: ${e.message}")
                e.printStackTrace()
            }
        }
    }

    private suspend fun listConfigs() {
        withContext(Dispatchers.IO) {
            chat("§9Available configurations:")

            val configFiles = settingsDir.listFiles { _, name -> name.endsWith(".json") }
            if (configFiles == null || configFiles.isEmpty()) {
                chat("§7No configurations found.")
                return@withContext
            }

            for (file in configFiles.sortedBy { it.name }) {
                val configName = file.nameWithoutExtension
                try {
                    val configObject = file.readJson() as? JsonObject
                    val saveTime = configObject?.get("SaveTime")?.asLong
                    val clientVersion = configObject?.get("ClientVersion")?.asString ?: "unknown"
                    
                    val timeInfo = if (saveTime != null) {
                        val date = java.util.Date(saveTime)
                        " §8(${java.text.SimpleDateFormat("MM-dd HH:mm").format(date)}, v$clientVersion)"
                    } else {
                        " §8(v$clientVersion)"
                    }
                    
                    chat("§7> §a$configName$timeInfo")
                } catch (e: Exception) {
                    chat("§7> §c$configName §8(corrupted)")
                }
            }
        }
    }

    private suspend fun deleteConfig(args: Array<String>) {
        withContext(Dispatchers.IO) {
            if (args.size <= 2) {
                chatSyntax("config delete <name>")
                return@withContext
            }

            val configName = args[2]
            val configFile = File(settingsDir, "$configName.json")

            if (!configFile.exists()) {
                chat("§cConfiguration '$configName' does not exist!")
                return@withContext
            }

            configFile.delete()
            chat("§aConfiguration '$configName' deleted successfully!")
        }
    }

    private suspend fun openConfigFolder() {
        withContext(Dispatchers.IO) {
            try {
                Desktop.getDesktop().open(settingsDir)
                chat("§aOpened configurations folder.")
            } catch (e: Exception) {
                chat("§cFailed to open folder: ${e.message}")
            }
        }
    }

    override fun tabComplete(args: Array<String>): List<String> {
        if (args.isEmpty()) return emptyList()

        return when (args.size) {
            1 -> listOf("save", "load", "list", "delete", "folder").filter { it.startsWith(args[0], true) }
            2 -> {
                when (args[0].lowercase()) {
                    "load", "delete" -> {
                        val configFiles = settingsDir.listFiles { _, name -> name.endsWith(".json") }
                        configFiles?.map { it.nameWithoutExtension }?.filter { it.startsWith(args[1], true) } ?: emptyList()
                    }
                    else -> emptyList()
                }
            }
            else -> emptyList()
        }
    }
}
